# -*- coding: utf-8 -*-
# NO视频插件 - 不负追剧好时光
import re
import sys
import json
import time
from urllib.parse import quote, unquote
from pyquery import PyQuery as pq
from base.spider import Spider

class Spider(Spider):

    def init(self, extend=""):
        pass

    def getName(self):
        return "NO视频"

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def action(self, action):
        pass

    def destroy(self):
        pass

    host = 'https://www.novipnoad.net'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'identity',  # 禁用压缩以避免Brotli问题
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
    }

    def homeContent(self, filter):
        try:
            response = self.fetch(self.host, headers=self.headers)
            data = self.getpq(response.text)
            
            result = {}
            classes = []
            
            # 添加分类
            categories = [
                {'type_name': '电影', 'type_id': 'movie'},
                {'type_name': '港剧', 'type_id': 'tv/hongkong'},
                {'type_name': '台剧', 'type_id': 'tv/taiwan'},
                {'type_name': '欧美剧', 'type_id': 'tv/western'},
                {'type_name': '日剧', 'type_id': 'tv/japan'},
                {'type_name': '韩剧', 'type_id': 'tv/korea'},
                {'type_name': '泰剧', 'type_id': 'tv/thailand'},
                {'type_name': '土耳其剧', 'type_id': 'tv/turkey'},
                {'type_name': '动画', 'type_id': 'anime'},
                {'type_name': '综艺', 'type_id': 'shows'},
                {'type_name': '音乐', 'type_id': 'music'},
                {'type_name': '短片', 'type_id': 'short'},
                {'type_name': '其他', 'type_id': 'other'}
            ]
            classes.extend(categories)
            
            # 获取首页推荐内容
            videos = []
            items = data('.video-item')

            for item in items.items():
                try:
                    # 提取视频链接
                    detail_link = item('a').attr('href')
                    if not detail_link:
                        continue

                    # 提取视频ID
                    vod_id = self.extract_id_from_url(detail_link)
                    if not vod_id:
                        continue

                    # 提取标题
                    title_elem = item('a')
                    vod_name = title_elem.attr('title') or item('h3 a').text().strip()
                    if not vod_name:
                        continue

                    # 清理标题
                    vod_name = self.clean_title(vod_name)

                    # 提取图片
                    img_elem = item('img')
                    vod_pic = img_elem.attr('data-original') or img_elem.attr('src')
                    if vod_pic and not vod_pic.startswith('http'):
                        if vod_pic.startswith('//'):
                            vod_pic = 'https:' + vod_pic
                        else:
                            vod_pic = self.host + vod_pic

                    # 提取备注信息
                    vod_remarks = ''
                    
                    # 年份暂时留空，在详情页获取
                    vod_year = ''

                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': vod_name,
                        'vod_pic': vod_pic,
                        'vod_year': vod_year,
                        'vod_remarks': vod_remarks
                    })
                except Exception as e:
                    self.log(f"解析首页视频项失败: {e}")
                    continue
            
            result['class'] = classes
            result['list'] = videos
            return result
            
        except Exception as e:
            self.log(f"获取首页内容失败: {e}")
            return {'class': [], 'list': []}

    def homeVideoContent(self):
        pass

    def categoryContent(self, tid, pg, filter, extend):
        try:
            # 构建分类URL
            if int(pg) == 1:
                url = f"{self.host}/{tid}/"
            else:
                url = f"{self.host}/{tid}/page/{pg}/"
            
            response = self.fetch(url, headers=self.headers)
            data = self.getpq(response.text)
            
            videos = []
            items = data('.video-item')

            for item in items.items():
                try:
                    # 提取视频链接
                    detail_link = item('a').attr('href')
                    if not detail_link:
                        continue

                    # 提取视频ID
                    vod_id = self.extract_id_from_url(detail_link)
                    if not vod_id:
                        continue

                    # 提取标题
                    title_elem = item('a')
                    vod_name = title_elem.attr('title') or item('h3 a').text().strip()
                    if not vod_name:
                        continue

                    # 清理标题
                    vod_name = self.clean_title(vod_name)

                    # 提取图片
                    img_elem = item('img')
                    vod_pic = img_elem.attr('data-original') or img_elem.attr('src')
                    if vod_pic and not vod_pic.startswith('http'):
                        if vod_pic.startswith('//'):
                            vod_pic = 'https:' + vod_pic
                        else:
                            vod_pic = self.host + vod_pic

                    # 提取备注信息
                    vod_remarks = ''
                    
                    # 年份暂时留空，在详情页获取
                    vod_year = ''

                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': vod_name,
                        'vod_pic': vod_pic,
                        'vod_year': vod_year,
                        'vod_remarks': vod_remarks
                    })
                except Exception as e:
                    self.log(f"解析分类视频项失败: {e}")
                    continue
            
            result = {
                'list': videos,
                'page': int(pg),
                'pagecount': 9999,  # NO视频没有明确的总页数，设置较大值
                'limit': 20,
                'total': 999999
            }
            return result
            
        except Exception as e:
            self.log(f"获取分类内容失败: {e}")
            return {'list': [], 'page': int(pg), 'pagecount': 0, 'limit': 20, 'total': 0}

    def detailContent(self, ids):
        try:
            vod_id = ids[0]
            # 根据ID构建详情页URL
            url = self.construct_detail_url(vod_id)

            response = self.fetch(url, headers=self.headers)
            data = self.getpq(response.text)

            # 提取基本信息
            title = data('h1').text().strip() or data('.entry-title').text().strip()
            if not title:
                # 尝试从页面标题提取
                page_title = data('title').text().strip()
                if page_title:
                    title = page_title.replace(' – NO视频', '').replace(' - NO视频', '').strip()

            # 提取图片
            pic = data('.wp-post-image').attr('src') or data('img').eq(0).attr('src')
            if pic and not pic.startswith('http'):
                if pic.startswith('//'):
                    pic = 'https:' + pic
                else:
                    pic = self.host + pic

            # 提取详细信息
            vod_year = ''
            vod_area = ''
            vod_lang = ''
            vod_director = ''
            vod_actor = ''
            type_name = ''

            # 从标题中提取年份
            year_match = re.search(r'\((\d{4})\)', title)
            if year_match:
                vod_year = year_match.group(1)

            # 从标题中提取类型
            if '【美剧】' in title:
                type_name = '美剧'
            elif '【韩剧】' in title:
                type_name = '韩剧'
            elif '【日剧】' in title:
                type_name = '日剧'
            elif '【泰剧】' in title:
                type_name = '泰剧'
            elif '【港剧】' in title:
                type_name = '港剧'
            elif '【台剧】' in title:
                type_name = '台剧'
            elif '【土耳其剧】' in title:
                type_name = '土耳其剧'
            elif '【动作】' in title or '【剧情】' in title or '【恐怖】' in title:
                type_name = '电影'
            elif '【动漫】' in title or '【动画】' in title:
                type_name = '动画'

            # 提取剧情简介
            vod_content = data('.entry-content p').text().strip()
            if not vod_content:
                vod_content = data('.post-content').text().strip()

            # 提取播放列表
            play_sources = []
            play_urls = []

            # 查找播放按钮和剧集列表
            multilink_buttons = data('.multilink-btn[data-vid]')
            if len(multilink_buttons) > 0:
                # 找到了剧集播放按钮
                episodes = []
                for button in multilink_buttons.items():
                    vid = button.attr('data-vid')
                    episode_name = button.text().strip()
                    if vid and episode_name:
                        # 构建播放URL，包含vid参数
                        play_url = f"{url}?vid={vid}"
                        episodes.append(f"{episode_name}${play_url}")

                if episodes:
                    play_sources.append("在线播放")
                    play_urls.append('#'.join(episodes))
                else:
                    # 备用方案
                    play_sources.append("默认播放")
                    play_urls.append(f"播放${url}")
            else:
                # 查找其他播放方式
                download_links = data('a[href*=".mp4"], a[href*=".m3u8"], a[href*="play"]')
                if len(download_links) > 0:
                    episodes = []
                    for i, link in enumerate(download_links.items()):
                        link_text = link.text().strip() or f"第{i+1}集"
                        link_url = link.attr('href')
                        if link_url:
                            if not link_url.startswith('http'):
                                link_url = self.host + link_url
                            episodes.append(f"{link_text}${link_url}")

                    if episodes:
                        play_sources.append("下载播放")
                        play_urls.append('#'.join(episodes))
                else:
                    # 默认播放方式
                    play_sources.append("默认播放")
                    play_urls.append(f"播放${url}")

            vod = {
                'vod_id': vod_id,
                'vod_name': self.clean_title(title),
                'vod_pic': pic,
                'type_name': type_name,
                'vod_year': vod_year,
                'vod_area': vod_area,
                'vod_lang': vod_lang,
                'vod_director': vod_director,
                'vod_actor': vod_actor,
                'vod_content': vod_content,
                'vod_play_from': '$$$'.join(play_sources),
                'vod_play_url': '$$$'.join(play_urls)
            }

            return {'list': [vod]}

        except Exception as e:
            self.log(f"获取详情内容失败: {e}")
            return {'list': []}

    def searchContent(self, key, quick, pg="1"):
        try:
            search_url = f"{self.host}/?s={quote(key)}"
            response = self.fetch(search_url, headers=self.headers)
            data = self.getpq(response.text)
            
            videos = []
            items = data('.video-item')

            for item in items.items():
                try:
                    # 提取视频链接
                    detail_link = item('a').attr('href')
                    if not detail_link:
                        continue

                    # 提取视频ID
                    vod_id = self.extract_id_from_url(detail_link)
                    if not vod_id:
                        continue

                    # 提取标题
                    title_elem = item('a')
                    vod_name = title_elem.attr('title') or item('h3 a').text().strip()
                    if not vod_name:
                        continue

                    # 清理标题
                    vod_name = self.clean_title(vod_name)

                    # 提取图片
                    img_elem = item('img')
                    vod_pic = img_elem.attr('data-original') or img_elem.attr('src')
                    if vod_pic and not vod_pic.startswith('http'):
                        if vod_pic.startswith('//'):
                            vod_pic = 'https:' + vod_pic
                        else:
                            vod_pic = self.host + vod_pic

                    # 提取备注信息
                    vod_remarks = ''
                    
                    # 年份暂时留空
                    vod_year = ''

                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': vod_name,
                        'vod_pic': vod_pic,
                        'vod_year': vod_year,
                        'vod_remarks': vod_remarks
                    })
                except Exception as e:
                    self.log(f"解析搜索结果项失败: {e}")
                    continue
            
            return {'list': videos, 'page': int(pg)}
            
        except Exception as e:
            self.log(f"搜索失败: {e}")
            return {'list': [], 'page': int(pg)}

    def playerContent(self, flag, id, vipFlags):
        try:
            # 解析播放URL，提取vid参数
            vid = None
            play_url = id

            if '?vid=' in id:
                play_url, vid_param = id.split('?vid=', 1)
                vid = vid_param
            else:
                play_url = self.construct_detail_url(id)

            # 获取详情页面
            response = self.fetch(play_url, headers=self.headers)
            content = response.text
            data = self.getpq(content)

            # 如果有vid参数，说明是点击了具体剧集
            if vid:
                self.log(f"尝试解析视频ID: {vid}")

                # 重要发现：需要先访问带vid参数的页面获取新的pkey
                vid_url = f"{play_url}?vid={vid}"
                self.log(f"访问带vid的页面: {vid_url}")

                vid_headers = self.headers.copy()
                vid_headers['Referer'] = play_url

                vid_response = self.fetch(vid_url, headers=vid_headers)
                vid_content = vid_response.text

                # 从带vid的页面中提取新的播放密钥
                pkey_match = re.search(r'window\.playInfo\s*=\s*\{\s*pkey\s*:\s*["\']([^"\']+)["\']', vid_content)
                if pkey_match:
                    pkey = pkey_match.group(1)
                    self.log(f"找到新的播放密钥: {pkey}")

                    # 尝试多种API请求方式
                    api_attempts = [
                        # 方式1: 标准AJAX请求
                        {
                            'url': f"{self.host}/lib/ajax.php",
                            'data': {'action': 'get_play_url', 'vid': vid, 'pkey': pkey},
                            'method': 'POST'
                        },
                        # 方式2: 简化参数
                        {
                            'url': f"{self.host}/lib/ajax.php",
                            'data': {'vid': vid, 'pkey': pkey},
                            'method': 'POST'
                        },
                        # 方式3: GET请求
                        {
                            'url': f"{self.host}/lib/ajax.php",
                            'data': {'action': 'get_play_url', 'vid': vid, 'pkey': pkey},
                            'method': 'GET'
                        }
                    ]

                    for attempt in api_attempts:
                        try:
                            ajax_headers = self.headers.copy()
                            ajax_headers.update({
                                'X-Requested-With': 'XMLHttpRequest',
                                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                                'Referer': play_url
                            })

                            if attempt['method'] == 'POST':
                                api_response = self.post(attempt['url'], data=attempt['data'], headers=ajax_headers)
                            else:
                                from urllib.parse import urlencode
                                get_url = f"{attempt['url']}?{urlencode(attempt['data'])}"
                                api_response = self.fetch(get_url, headers=ajax_headers)

                            if api_response and api_response.text.strip():
                                response_text = api_response.text.strip()
                                self.log(f"API响应: {response_text}")

                                # 如果响应不是"0"，尝试解析
                                if response_text != "0":
                                    try:
                                        # 尝试JSON解析
                                        api_result = json.loads(response_text)
                                        if isinstance(api_result, dict):
                                            # 查找播放地址
                                            for key in ['url', 'play_url', 'video_url', 'src', 'link']:
                                                if key in api_result and api_result[key]:
                                                    video_url = api_result[key]
                                                    if 'http' in video_url:
                                                        self.log(f"找到播放地址: {video_url}")
                                                        return {
                                                            "parse": 0,
                                                            "url": video_url,
                                                            "header": self.headers
                                                        }
                                    except:
                                        # 可能是直接的URL
                                        if 'http' in response_text and ('.m3u8' in response_text or '.mp4' in response_text):
                                            self.log(f"找到直接播放地址: {response_text}")
                                            return {
                                                "parse": 0,
                                                "url": response_text,
                                                "header": self.headers
                                            }
                        except Exception as e:
                            self.log(f"API请求失败: {e}")
                            continue

                    # 如果API都失败了，尝试从带vid页面中直接提取播放地址
                    self.log("API请求都失败，尝试从带vid页面提取播放地址...")

                    # 在带vid页面中查找播放地址
                    vid_url_patterns = [
                        r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)',
                        r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)',
                        r'["\']url["\']:\s*["\']([^"\']+)["\']',
                        r'["\']src["\']:\s*["\']([^"\']+)["\']',
                        r'video_url["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    ]

                    for pattern in vid_url_patterns:
                        matches = re.findall(pattern, vid_content)
                        for match in matches:
                            if isinstance(match, tuple):
                                match = match[0] if match[0] else match[1]
                            if 'http' in match and ('.m3u8' in match or '.mp4' in match):
                                self.log(f"在带vid页面找到播放地址: {match}")
                                return {
                                    "parse": 0,
                                    "url": match,
                                    "header": self.headers
                                }
                else:
                    self.log("未找到播放密钥")

            # 备用方案1: 查找页面中的播放地址
            self.log("尝试从页面中提取播放地址...")

            # 查找m3u8链接
            m3u8_pattern = r'(https?://[^"\'>\s]+\.m3u8[^"\'>\s]*)'
            m3u8_match = re.search(m3u8_pattern, content)

            if m3u8_match:
                video_url = m3u8_match.group(1)
                self.log(f"找到M3U8地址: {video_url}")
                return {
                    "parse": 0,
                    "url": video_url,
                    "header": self.headers
                }

            # 查找mp4链接
            mp4_pattern = r'(https?://[^"\'>\s]+\.mp4[^"\'>\s]*)'
            mp4_match = re.search(mp4_pattern, content)

            if mp4_match:
                video_url = mp4_match.group(1)
                self.log(f"找到MP4地址: {video_url}")
                return {
                    "parse": 0,
                    "url": video_url,
                    "header": self.headers
                }

            # 备用方案2: 查找iframe播放器
            iframe_pattern = r'<iframe[^>]+src=["\']([^"\']+)["\'][^>]*>'
            iframe_match = re.search(iframe_pattern, content)

            if iframe_match:
                iframe_url = iframe_match.group(1)
                if not iframe_url.startswith('http'):
                    if iframe_url.startswith('//'):
                        iframe_url = 'https:' + iframe_url
                    else:
                        iframe_url = self.host + iframe_url

                self.log(f"找到iframe播放器: {iframe_url}")
                return {
                    "parse": 1,
                    "url": iframe_url,
                    "header": self.headers
                }

            # 最后方案: 返回带vid参数的页面让系统进一步解析
            if vid:
                # 返回带vid参数的页面，这是最接近真实播放页面的URL
                final_url = f"{play_url}?vid={vid}"
                self.log(f"返回带vid参数的播放页面: {final_url}")
                return {
                    "parse": 1,
                    "url": final_url,
                    "header": self.headers
                }
            else:
                # 返回详情页面
                self.log("返回详情页面")
                return {
                    "parse": 1,
                    "url": play_url,
                    "header": self.headers
                }

        except Exception as e:
            self.log(f"获取播放地址失败: {e}")
            return {
                "parse": 1,
                "url": self.construct_detail_url(id),
                "header": self.headers
            }

    def getpq(self, data):
        """获取pyquery对象"""
        try:
            return pq(data)
        except Exception as e:
            self.log(f"解析HTML失败: {e}")
            return pq("")

    def extract_id_from_url(self, url):
        """从URL中提取视频ID"""
        try:
            # NO视频的URL格式: /tv/western/150717.html 或 /movie/150633.html
            match = re.search(r'/(\w+/\w+/\d+)\.html', url)
            if match:
                return match.group(1)
            
            # 备用匹配模式
            match = re.search(r'/(\w+/\d+)\.html', url)
            if match:
                return match.group(1)
                
            return url
        except:
            return url

    def construct_detail_url(self, vod_id):
        """根据视频ID构建详情页URL"""
        if vod_id.startswith('http'):
            return vod_id
        return f"{self.host}/{vod_id}.html"

    def clean_title(self, title):
        """清理视频标题"""
        if not title:
            return ""

        # 移除常见的多余文本
        patterns_to_remove = [
            r'【[^】]*】',  # 移除方括号内容，如【美剧】【官方中字】
            r'\([^)]*\)',  # 移除圆括号内容，如(6集全)
            r'NO视频.*?$',  # 移除NO视频相关
            r'www\.novipnoad\.net.*?$',  # 移除网站域名
            r'–.*?$',  # 移除破折号后内容
            r'-.*?$',   # 移除减号后内容
            r'_.*?$',   # 移除下划线后内容
            r'高清在线观看.*?$',  # 移除观看相关文字
            r'在线观看.*?$',
            r'免费观看.*?$',
            r'完整版.*?$',
            r'全集.*?$',
        ]

        cleaned = title
        for pattern in patterns_to_remove:
            cleaned = re.sub(pattern, '', cleaned).strip()

        # 进一步清理多余空格
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        return cleaned if cleaned else title
